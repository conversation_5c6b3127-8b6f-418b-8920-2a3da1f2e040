#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "math.h"


#include "main.h"
#include "usart.h"
#include "scheduler.h"
#include "ringbuffer.h"
#include "oled.h"
#include "u8g2.h"
#include "i2c.h"
#include "WouoUI.h"      // WouoUI 核心框架
#include "WouoUI_user.h" // 用户自定义的菜单结构和回调函数 (通常需要用户创建或修改)
#include "gd25qxx.h"
#include "lfs_port.h"
#include "lfs.h"


#include "led_app.h"
#include "key_app.h"
#include "btn_app.h"
#include "usart_app.h"
#include "ADC_app.h"
#include "oled_app.h"
#include "flash_app.h"


extern uint8_t ucLed[6];
extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_dma_buffer[128];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern struct rt_ringbuffer ringbuffer;
extern uint8_t rb_buffer[128];
extern ADC_HandleTypeDef hadc1;
extern struct lfs_config cfg;
extern lfs_t lfs;


extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim6;
extern TIM_HandleTypeDef htim14;
extern DAC_HandleTypeDef hdac;
extern DMA_HandleTypeDef hdma_dac1;
extern SPI_HandleTypeDef hspi2;


extern u8g2_t u8g2;




