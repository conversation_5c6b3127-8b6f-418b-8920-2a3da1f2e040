#include "flash_app.h"
#include "fatfs.h"
#include "ff.h"


// 假设SPI2和相关GPIO已通过CubeMX初始化
// extern SPI_HandleTypeDef hspi2; (确保在main.c或对应位置有定义)

void test_spi_flash(void) {
    uint32_t flash_id;
    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t test_addr = 0x000000; // 测试地址，选择一个扇区的起始

    my_printf(&huart1,"SPI FLASH Test Start\r\n");

    // 1. 初始化SPI Flash驱动 (主要是CS引脚状态)
    spi_flash_init();
    my_printf(&huart1,"SPI Flash Initialized.\r\n");

    // 2. 读取Flash ID
    flash_id = spi_flash_read_id();
    my_printf(&huart1,"Flash ID: 0x%lX\r\n", flash_id);
    // 你可以根据你的芯片手册检查ID是否正确，例如 GD25Q64的ID可能是 0xC84017

    // 3. 擦除一个扇区 (大小通常为4KB)
    // 注意：擦除操作耗时较长
    my_printf(&huart1,"Erasing sector at address 0x%lX...\r\n", test_addr);
    spi_flash_sector_erase(test_addr);
    my_printf(&huart1,"Sector erased.\r\n");

    // (可选) 校验擦除：读取一页数据，检查是否全为0xFF
    spi_flash_buffer_read(read_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    int erased_check_ok = 1;
    for (int i = 0; i < SPI_FLASH_PAGE_SIZE; i++) {
        if (read_buffer[i] != 0xFF) {
            erased_check_ok = 0;
            break;
        }
    }
    if (erased_check_ok) {
        my_printf(&huart1,"Erase check PASSED. Sector is all 0xFF.\r\n");
    } else {
        my_printf(&huart1,"Erase check FAILED.\r\n");
    }

    // 4. 准备写入数据 (写入一页)
    const char* message = "Hello from STM32 to SPI FLASH! Microunion Studio Test - 12345.";
    uint16_t data_len = strlen(message);
    if (data_len >= SPI_FLASH_PAGE_SIZE) {
        data_len = SPI_FLASH_PAGE_SIZE -1; // 确保不超过页大小
    }
    memset(write_buffer, 0, SPI_FLASH_PAGE_SIZE);
    memcpy(write_buffer, message, data_len);
    write_buffer[data_len] = '\0'; // 确保字符串结束

    my_printf(&huart1,"Writing data to address 0x%lX: \"%s\"\r\n", test_addr, write_buffer);
    // 使用 spi_flash_buffer_write (可以处理跨页，但这里只写一页内)
    // 或者直接用 spi_flash_page_write 如果确定在一页内
    spi_flash_buffer_write(write_buffer, test_addr, SPI_FLASH_PAGE_SIZE); // 写入整页数据，包括填充的0
    // spi_flash_page_write(write_buffer, test_addr, data_len + 1); // 只写入有效数据
    my_printf(&huart1,"Data written.\r\n");

    // 5. 读取写入的数据
    my_printf(&huart1,"Reading data from address 0x%lX...\r\n", test_addr);
    memset(read_buffer, 0, SPI_FLASH_PAGE_SIZE);
    spi_flash_buffer_read(read_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    my_printf(&huart1,"Data read: \"%s\"\r\n", read_buffer);

    // 6. 校验数据
    if (memcmp(write_buffer, read_buffer, SPI_FLASH_PAGE_SIZE) == 0) {
        my_printf(&huart1,"Data VERIFIED! Write and Read successful.\r\n");
    } else {
        my_printf(&huart1,"Data VERIFICATION FAILED!\r\n");
    }

    my_printf(&huart1,"SPI FLASH Test End\r\n");
}

// 在你的main函数中合适的位置调用 test_spi_flash()
// int main(void) {
//   ...
//   MX_SPI2_Init(); // CubeMX生成的SPI初始化
//   MX_GPIO_Init(); // CubeMX生成的GPIO初始化 (包括CS引脚)
//   test_spi_flash();
//   while(1) {}



lfs_t lfs;
struct lfs_config cfg;


// LittleFS test function. Author: Microunion Studio
void lfs_basic_test(void)
{
    my_printf(&huart1, "\r\n--- LittleFS File System Test ---\r\n");
    int err = lfs_mount(&lfs, &cfg);
    if (err)
    { // reformat if mount fails (e.g. first boot)
        my_printf(&huart1, "LFS: Mount failed(%d), formatting...\n", err);
        if (lfs_format(&lfs, &cfg) || (err = lfs_mount(&lfs, &cfg)))
        {
            my_printf(&huart1, "LFS: Format/Mount failed(%d)!\n", err);
            return;
        }
        my_printf(&huart1, "LFS: Format & Mount OK.\n");
    }
    else
    {
        my_printf(&huart1, "LFS: Mount successful.\n");
    }

    // Create boot directory if it doesn't exist
    err = lfs_mkdir(&lfs, "boot");
    if (err && err != LFS_ERR_EXIST)
    {
        my_printf(&huart1, "LFS: Failed to create 'boot' directory(%d)!\n", err);
        goto end_test;
    }
    if (err == LFS_ERR_OK)
    {
        my_printf(&huart1, "LFS: Directory 'boot' created successfully.\n");
    }

    uint32_t boot_count = 0;
    lfs_file_t file;
    const char *filename = "boot/boot_cnt.txt";
    err = lfs_file_open(&lfs, &file, filename, LFS_O_RDWR | LFS_O_CREAT);
    if (err)
    {
        my_printf(&huart1, "LFS: Failed to open file '%s'(%d)!\n", filename, err);
        goto end_test;
    }

    lfs_ssize_t r_sz = lfs_file_read(&lfs, &file, &boot_count, sizeof(boot_count));
    if (r_sz < 0)
    { // Read error
        my_printf(&huart1, "LFS: Failed to read file '%s'(%ld), initializing counter.\n", filename, (long)r_sz);
        boot_count = 0;
    }
    else if (r_sz != sizeof(boot_count))
    { // Partial read or empty file
        my_printf(&huart1, "LFS: Read %ld bytes from '%s' (expected %d), initializing counter.\n", (long)r_sz, filename, (int)sizeof(boot_count));
        boot_count = 0;
    } // Else, successfully read previous count

    boot_count++;
    my_printf(&huart1, "LFS: File '%s' current boot count: %lu\n", filename, boot_count);

    err = lfs_file_rewind(&lfs, &file);
    if (err)
    {
        my_printf(&huart1, "LFS: Failed to rewind file '%s'(%d)!\n", filename, err);
        lfs_file_close(&lfs, &file);
        goto end_test;
    }

    lfs_ssize_t w_sz = lfs_file_write(&lfs, &file, &boot_count, sizeof(boot_count));
    if (w_sz < 0)
    {
        my_printf(&huart1, "LFS: Failed to write file '%s'(%ld)!\n", filename, (long)w_sz);
    }
    else if (w_sz != sizeof(boot_count))
    {
        my_printf(&huart1, "LFS: Partial write to '%s' (%ld/%d bytes)!\n", filename, (long)w_sz, (int)sizeof(boot_count));
    }
    else
    {
        my_printf(&huart1, "LFS: File '%s' updated successfully.\n", filename);
    }

    if (lfs_file_close(&lfs, &file))
    {
        my_printf(&huart1, "LFS: Failed to close file '%s'!\n", filename);
    }

//    // 使用新的文件系统遍历函数显示整个文件系统结构
//    my_printf(&huart1, "\r\n[File System Structure]\r\n");
//    my_printf(&huart1, "/ (root directory)\r\n");
//    list_dir_recursive("/", 0);

end_test:
    my_printf(&huart1, "--- LittleFS File System Test End ---\r\n");
    // lfs_unmount(&lfs); // Optional: Unmount if needed, often not done if MCU resets.
}


void test_sd_fatfs(void)
{
    FRESULT res;           // FatFs操作结果码
    DIR dir;               // 目录对象
    FILINFO fno;           // 文件信息对象
    uint32_t byteswritten; // 写入字节计数
    uint32_t bytesread;    // 读取字节计数
    char ReadBuffer[256];  // 读取缓冲区
    char WriteBuffer[] = "米醋电子工作室 - GD32 SD卡FATFS测试数据，如果你能看到这条消息，说明SD卡文件系统工作正常！";
    UINT bw, br;                              // 读写字节数计数
    const char *TestFileName = "SD_TEST.TXT"; // 测试文件名

    my_printf(&huart1, "\r\n--- SD卡FATFS文件系统测试开始 ---\r\n");

    // 挂载文件系统
    if (f_mount(&SDFatFS, SDPath, 1) != FR_OK)
    {
        my_printf(&huart1, "SD卡挂载失败，请检查SD卡连接或是否初始化\r\n");
        return;
    }
    my_printf(&huart1, "SD卡挂载成功\r\n");

    // 创建测试目录（如果不存在）
    res = f_mkdir("测试目录");
    if (res == FR_OK)
    {
        my_printf(&huart1, "创建测试目录成功\r\n");
    }
    else if (res == FR_EXIST)
    {
        my_printf(&huart1, "测试目录已存在\r\n");
    }
    else
    {
        my_printf(&huart1, "创建目录失败，错误码: %d\r\n", res);
    }

    // 创建并写入测试文件
    my_printf(&huart1, "创建并写入测试文件...\r\n");
    res = f_open(&SDFile, TestFileName, FA_CREATE_ALWAYS | FA_WRITE);
    if (res == FR_OK)
    {
        // 写入数据
        res = f_write(&SDFile, WriteBuffer, strlen(WriteBuffer), &bw);
        if (res == FR_OK && bw == strlen(WriteBuffer))
        {
            my_printf(&huart1, "写入文件成功: %u 字节\r\n", bw);
        }
        else
        {
            my_printf(&huart1, "写入文件失败，错误码: %d\r\n", res);
        }

        // 关闭文件
        f_close(&SDFile);
    }
    else
    {
        my_printf(&huart1, "创建文件失败，错误码: %d\r\n", res);
    }

    // 读取测试文件
    my_printf(&huart1, "读取测试文件...\r\n");
    memset(ReadBuffer, 0, sizeof(ReadBuffer));
    res = f_open(&SDFile, TestFileName, FA_READ);
    if (res == FR_OK)
    {
        // 读取数据
        res = f_read(&SDFile, ReadBuffer, sizeof(ReadBuffer) - 1, &br);
        if (res == FR_OK)
        {
            ReadBuffer[br] = '\0'; // 确保字符串结束符
            my_printf(&huart1, "读取文件成功: %u 字节\r\n", br);
            my_printf(&huart1, "文件内容: %s\r\n", ReadBuffer);

            // 验证数据一致性
            if (strcmp(ReadBuffer, WriteBuffer) == 0)
            {
                my_printf(&huart1, "数据验证成功: 读写数据一致\r\n");
            }
            else
            {
                my_printf(&huart1, "数据验证失败: 读写数据不一致\r\n");
            }
        }
        else
        {
            my_printf(&huart1, "读取文件失败，错误码: %d\r\n", res);
        }

        // 关闭文件
        f_close(&SDFile);
    }
    else
    {
        my_printf(&huart1, "打开文件失败，错误码: %d\r\n", res);
    }

    // 列出根目录文件
    my_printf(&huart1, "\r\n根目录文件列表:\r\n");
    res = f_opendir(&dir, "/");
    if (res == FR_OK)
    {
        for (;;)
        {
            // 读取目录项
            res = f_readdir(&dir, &fno);
            if (res != FR_OK || fno.fname[0] == 0)
                break;

            // 显示文件/目录信息
            if (fno.fattrib & AM_DIR)
            {
                my_printf(&huart1, "[DIR] %s\r\n", fno.fname);
            }
            else
            {
                my_printf(&huart1, "[FILE] %s (%lu 字节)\r\n", fno.fname, (unsigned long)fno.fsize);
            }
        }
        f_closedir(&dir);
    }
    else
    {
        my_printf(&huart1, "打开目录失败，错误码: %d\r\n", res);
    }

    // 完成测试，卸载文件系统
    f_mount(NULL, SDPath, 0);
    my_printf(&huart1, "--- SD卡FATFS文件系统测试结束 ---\r\n");
}




