#include "btn_app.h"
#include "ebtn.h"
#include "stdio.h"
#include "WouoUI.h"


uint8_t copyled[18];

/* 1. 定义按键参数实例 */
// 参数宏: EBTN_PARAMS_INIT(
//     按下消抖时间ms, 释放消抖时间ms,
//     单击有效最短按下时间ms, 单击有效最长按下时间ms,
//     多次单击最大间隔时间ms,
//     长按(KeepAlive)事件周期ms (0禁用),
//     最大连续有效点击次数 (e.g., 1=单击, 2=双击, ...)
// )
const ebtn_btn_param_t key_param_normal = EBTN_PARAMS_INIT(
    20,     // time_debounce: 按下稳定 20ms
    20,     // time_debounce_release: 释放稳定 20ms
    50,     // time_click_pressed_min: 最短单击按下 50ms
    500,    // time_click_pressed_max: 最长单击按下 500ms (超过则不算单击)
    300,    // time_click_multi_max: 多次单击最大间隔 300ms (两次点击间隔超过则重新计数)
    500,    // time_keepalive_period: 长按事件周期 500ms (按下超过 500ms 后，每 500ms 触发一次)
    5       // max_consecutive: 最多支持 5 连击
);

/* 2. 定义静态按键列表 */
// 宏: EBTN_BUTTON_INIT(按键ID, 参数指针)
ebtn_btn_t static_buttons[] = {
    EBTN_BUTTON_INIT(1, &key_param_normal), // KEY1, ID=1, 使用 'key_param_normal' 参数
    EBTN_BUTTON_INIT(2, &key_param_normal),	// KEY2, ID=2, 也使用 'key_param_normal' 参数
		EBTN_BUTTON_INIT(3, &key_param_normal),		
		EBTN_BUTTON_INIT(4, &key_param_normal), 
		EBTN_BUTTON_INIT(5, &key_param_normal),		
		EBTN_BUTTON_INIT(6, &key_param_normal), 
};

/* 3. 定义静态组合按键列表 (可选) */
// 宏: EBTN_BUTTON_COMBO_INIT(按键ID, 参数指针)
ebtn_btn_combo_t static_combos[] = {
    // 假设 KEY1+KEY2 组合键
    EBTN_BUTTON_COMBO_INIT(102, &key_param_normal), // 组合键, ID=102 (必须与普通按键ID不同)
		// 假设 KEY1+KEY3 组合键
		EBTN_BUTTON_COMBO_INIT(103, &key_param_normal),
		// 假设 KEY1+KEY4 组合键
		EBTN_BUTTON_COMBO_INIT(104, &key_param_normal),
};




/* 1. 实现获取按键状态的回调函数 */
// 函数原型: uint8_t (*ebtn_get_state_fn)(struct ebtn_btn *btn);
uint8_t my_get_key_state(struct ebtn_btn *btn) {
    // 根据传入的按钮实例中的 key_id 判断是哪个物理按键
    switch (btn->key_id) {
        case 1: // 请求读取 KEY1 的状态
            // 假设 KEY1 接在 PB0，按下为低电平 (返回 1 代表按下)
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7) == GPIO_PIN_RESET);
        case 2: // 请求读取 KEY2 的状态
            // 假设 KEY2 接在 PB1，按下为低电平
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_8) == GPIO_PIN_RESET);
				case 3:
						return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9) == GPIO_PIN_RESET);
				case 4:
						return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_10) == GPIO_PIN_RESET);
				case 5:
						return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11) == GPIO_PIN_RESET);
        case 6: // 请求读取 KEY2 的状态
            // 假设 KEY2 接在 PB1，按下为低电平
            return (HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_12) == GPIO_PIN_RESET);
        // ... 可以添加更多按键的读取逻辑 ...
        default:
            // 对于库内部处理组合键等情况，或者未知的 key_id，安全起见返回 0 (未按下)
            return 0;
    }
    // 注意：返回值 1 表示 "活动/按下"，0 表示 "非活动/释放"
}





// ebtn 按键事件回调函数
void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    // 示例：假设 Button 0 按键单击事件对应菜单"上"操作
    if ((btn->key_id == 1) && (evt == EBTN_EVT_ONCLICK))
    { 
        // ... (你自己的其他按键处理，例如切换 LED) ...
        // 发送"上"消息给 WouoUI
        WOUOUI_MSG_QUE_SEND(msg_up);
    }
    
    // 示例：假设 Button 1 按键单击事件对应菜单"下"操作
    if ((btn->key_id == 2) && (evt == EBTN_EVT_ONCLICK))
    { 
        // ... 
        WOUOUI_MSG_QUE_SEND(msg_down);
    }
    
    // 示例：假设 Button 2 按键单击事件对应菜单"左"操作
    if ((btn->key_id == 3) && (evt == EBTN_EVT_ONCLICK))
    { 
        // ... 
        WOUOUI_MSG_QUE_SEND(msg_left);
    }
    
    // 示例：假设 Button 3 按键单击事件对应菜单"右"操作
    if ((btn->key_id == 4) && (evt == EBTN_EVT_ONCLICK))
    { 
        // ... 
        WOUOUI_MSG_QUE_SEND(msg_right);
    }
    
    // 示例：假设 Button 4 按键单击事件对应菜单"返回"操作
    if ((btn->key_id == 5) && (evt == EBTN_EVT_ONCLICK))
    { 
        // ... 
        WOUOUI_MSG_QUE_SEND(msg_return);
    }
    
    // 示例：假设 Button 5 按键单击事件对应菜单"确认"操作
    if ((btn->key_id == 6) && (evt == EBTN_EVT_ONCLICK))
    { 
        // ... 
        WOUOUI_MSG_QUE_SEND(msg_click);
    }
    
    // 可以添加对长按、双击等事件的处理，并映射到 WouoUI 消息
    // if ((btn->key_id == USER_BUTTON_X) && (evt == EBTN_EVT_LONG_PRESS_START)) { ... }
}




void my_system_init(void) {
    // ... 其他硬件和外设初始化 (GPIO, 时钟, 定时器等) ...

    // 初始化 ebtn 库
    ebtn_init(
        static_buttons,                 // 静态按键数组的指针
        EBTN_ARRAY_SIZE(static_buttons), // 静态按键数量 (用宏计算)
        static_combos,                  // 静态组合按键数组的指针 (如果没有，传 NULL, 0)
        EBTN_ARRAY_SIZE(static_combos), // 静态组合按键数量 (如果没有，传 0)
        my_get_key_state,               // 你的状态获取回调函数
        prv_btn_event             // 你的事件处理回调函数
    );
	
		ebtn_set_config(EBTN_CFG_COMBO_PRIORITY);//启用组合键优先

    // --- 配置组合键 (如果使用了组合键) ---
    // 1. 找到参与组合的普通按键的内部索引 (Index)
    //    注意：这个内部索引不一定等于你设置的 key_id！
    int key1_index = ebtn_get_btn_index_by_key_id(1); // 获取 KEY1 (ID=1) 的内部索引
    int key2_index = ebtn_get_btn_index_by_key_id(2); // 获取 KEY2 (ID=2) 的内部索引
		int key3_index = ebtn_get_btn_index_by_key_id(3);
		int key4_index = ebtn_get_btn_index_by_key_id(4);
		int key5_index = ebtn_get_btn_index_by_key_id(5);
		int key6_index = ebtn_get_btn_index_by_key_id(6);
    // 2. 将这些索引对应的按键添加到组合键定义中
    //    确保索引有效 (>= 0)
    if (key1_index >= 0 && key2_index >= 0 && key3_index >= 0 && key4_index >= 0 && key5_index >= 0 && key6_index >= 0) {
        // 假设 static_combos[0] 是我们定义的 ID=102 的组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[0], key1_index); // 将 KEY1 添加到组合键
        ebtn_combo_btn_add_btn_by_idx(&static_combos[0], key2_index); // 将 KEY2 添加到组合键
			
        ebtn_combo_btn_add_btn_by_idx(&static_combos[1], key1_index); 
        ebtn_combo_btn_add_btn_by_idx(&static_combos[1], key3_index); 
			
        ebtn_combo_btn_add_btn_by_idx(&static_combos[2], key1_index); 
        ebtn_combo_btn_add_btn_by_idx(&static_combos[2], key4_index); 
    } 

				HAL_TIM_Base_Start_IT(&htim14);
}

void  HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
		if(htim == (&htim14))
		{
			ebtn_process(uwTick);
		}
}


void btn_task(void)
{
//	ebtn_process(HAL_GetTick());
}


//按键0+按键1 复制ucled
//按键0+按键2 粘贴ucled
//按键0+按键3 剪切ucled（复制完后全部熄灭）


