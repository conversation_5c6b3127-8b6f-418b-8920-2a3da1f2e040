#include "key_app.h"

uint8_t key_val,key_down,key_up,key_old;


uint8_t key_read(void)
{
	uint8_t key_value=0;
	
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7) == GPIO_PIN_RESET) key_value = 1;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_8) == GPIO_PIN_RESET) key_value = 2;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9) == GPIO_PIN_RESET) key_value = 3;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_10) == GPIO_PIN_RESET) key_value = 4;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11) == GPIO_PIN_RESET) key_value = 5;
	if(HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_12) == GPIO_PIN_RESET) key_value = 6;
	
	return key_value;
}


void key_task(void)
{
	key_val = key_read();
	key_down = key_val & (key_old ^ key_val);
	key_up = ~key_val & (key_old ^ key_val);
	key_old = key_val;
	
	if(key_down==1)
	{
		ucLed[5] ^= 1;		
	}
}



