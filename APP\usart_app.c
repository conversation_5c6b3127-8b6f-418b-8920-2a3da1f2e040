#include "usart_app.h"

uint16_t uart_rx_index = 0;
uint32_t uart_rx_ticks = 0;
uint8_t uart_rx_buffer[128] = {0};

uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
uint8_t uart_flag;

struct rt_ringbuffer ringbuffer;//定义环形缓冲区结构体
uint8_t rb_buffer[128];//环形缓冲区接收数组  缓冲区内存


//此函数用于单片机将数据发给上位机  单片机-发   电脑-收
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; // 临时存储格式化后的字符串
	va_list arg;      // 处理可变参数
	int len;          // 最终字符串长度

	va_start(arg, format);
	// 安全地格式化字符串到 buffer
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// 通过 HAL 库发送 buffer 中的内容
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}



////串口接收方法
////1超时解析法
//void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
//{
//    // 1. 核对身份：是 USART1 的快递员吗？
//	if (huart->Instance == USART1)
//	{
//        // 2. 更新收货时间：记录下当前时间
//		uart_rx_ticks = uwTick;
//        // 3. 货物入库：将收到的字节放入缓冲区（HAL库已自动完成）
//        //    并增加计数器
//        //    (注意：实际入库由 HAL_UART_Receive_IT 触发，这里只更新计数)
//		uart_rx_index++;
//        // 4. 准备下次收货：再次告诉硬件，我还想收一个字节
//		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
//	}
//}



////2DMA+空闲中断法
///**
// * @brief UART DMA接收完成或空闲事件回调函数
// * @param huart UART句柄
// * @param Size 指示在事件发生前，DMA已经成功接收了多少字节的数据
// * @retval None
// */
//void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
//{
//    // 1. 确认是目标串口 (USART1)
//    if (huart->Instance == USART1)
//    {
//        // 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
//        //    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
//        HAL_UART_DMAStop(huart);

//        // 3. 将 DMA 缓冲区中有效的数据 (Size 个字节) 复制到待处理缓冲区
//        memcpy(uart_dma_buffer, uart_rx_dma_buffer, Size); 
//        // 注意：这里使用了 Size，只复制实际接收到的数据
//        
//        // 4. 举起"到货通知旗"，告诉主循环有数据待处理
//        uart_flag = 1;

//        // 5. 清空 DMA 接收缓冲区，为下次接收做准备
//        //    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
//        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

//        // 6. **关键：重新启动下一次 DMA 空闲接收**
//        //    必须再次调用，否则只会接收这一次
//        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
//        
//        // 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
//         __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
//    }
//}



//3 ringbuffer+DMA+空闲中断法
void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart->Instance == USART1)
    {
        HAL_UART_DMAStop(huart);
								
				//将DMA接收到的数据写入环形缓冲区
				rt_ringbuffer_put(&ringbuffer,uart_rx_dma_buffer,Size);
			
				//清空 DMA 缓冲区（可选）
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));
				
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
        
         __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
}


//超时解析法和DMA+空闲中断解析法的任务函数不同，根据需要通过参考官网进行修改
void uart_task(void)
{
//	// 检查货架：如果计数器为0，说明没货或刚处理完，休息。
//	if (uart_rx_index == 0)
//		return;

//    // 检查手表：当前时间 - 最后收货时间 > 规定的超时时间？
//	if (uwTick - uart_rx_ticks > 100) // 核心判断  100ms
//	{
//        // 超时！开始理货 --- 
//        // "uart_rx_buffer" 里从第0个到第 "uart_rx_index - 1" 个
//        // 就是我们等到的一整批货（一帧数据）
//		my_printf(&huart1, "uart data: %s\n", uart_rx_buffer);
//        // (在这里加入你自己的处理逻辑，比如解析命令控制LED)
//        // --- 理货结束 --- 

//		// 清理现场：把处理完的货从货架上拿走，计数器归零
//		memset(uart_rx_buffer, 0, uart_rx_index);
//		uart_rx_index = 0;
//		
////		huart1.pRxBuffPtr = uart_rx_buffer;
//	}


	
	
//	if (uart_flag == 0)
//		return;

//	uart_flag = 0;
//	
//		my_printf(&huart1,"uart data: %s\n", uart_dma_buffer);
//	
//	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));		
//	if (uart_flag == 0)
//		return;



uint16_t length;

length = rt_ringbuffer_data_len(&ringbuffer);

if(length==0) return;

rt_ringbuffer_get(&ringbuffer,uart_dma_buffer,length);

my_printf(&huart1, "uart data: %s\n", uart_dma_buffer);

memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));


//		uint8_t temp_buf[128] = {0}; // 临时缓冲区
//    rt_size_t len = 0;

//    // 从环形缓冲区读取数据
//    len = rt_ringbuffer_get(&ringbuffer, temp_buf, sizeof(temp_buf));

//    if (len > 0)
//    {
////        // 添加字符串终止符（防止数据残留）
////        if (len < sizeof(temp_buf))
////            temp_buf[len] = '\0';
////        else
////            temp_buf[sizeof(temp_buf) - 1] = '\0';

//        // 打印数据
//        my_printf(&huart1, "uart data: %s\n", temp_buf);

//        // 清空临时缓冲区（可选）
//        memset(temp_buf, 0, sizeof(temp_buf));
//		}
////			1. 上位机发送 "reset"
////			2. DMA 接收数据到 uart_rx_dma_buffer
////			3. 空闲中断触发 → 数据写入环形缓冲区 → 重启 DMA
////			4. 主循环读取环形缓冲区 → 数据存入 temp_buf → 添加 '\0' → 打印
////			5. 下次接收时 DMA 重新指向干净的 uart_rx_dma_buffer
}



