#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.Channel-1\#ChannelInjectedConversion=ADC_CHANNEL_10
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.ContinuousConvMode=DISABLE
ADC1.DMAContinuousRequests=DISABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,master,NbrOfConversion,InjectedRank-1\#ChannelInjectedConversion,Channel-1\#ChannelInjectedConversion,SamplingTime-1\#ChannelInjectedConversion,InjectedOffset-1\#ChannelInjectedConversion,InjNumberOfConversion,ContinuousConvMode,DMAContinuousRequests,ExternalTrigConv,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion
ADC1.InjNumberOfConversion=1
ADC1.InjectedOffset-1\#ChannelInjectedConversion=0
ADC1.InjectedRank-1\#ChannelInjectedConversion=1
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-2\#ChannelRegularConversion=2
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-1\#ChannelInjectedConversion=ADC_SAMPLETIME_480CYCLES
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_480CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Trigger=DAC_TRIGGER_T6_TRGO
DAC.IPParameters=DAC_Trigger
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.1.Instance=DMA2_Stream0
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_NORMAL
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Priority=DMA_PRIORITY_LOW
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.DAC1.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.DAC1.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.DAC1.2.Instance=DMA1_Stream5
Dma.DAC1.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.DAC1.2.MemInc=DMA_MINC_ENABLE
Dma.DAC1.2.Mode=DMA_CIRCULAR
Dma.DAC1.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.DAC1.2.PeriphInc=DMA_PINC_DISABLE
Dma.DAC1.2.Priority=DMA_PRIORITY_LOW
Dma.DAC1.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=USART1_RX
Dma.Request1=ADC1
Dma.Request2=DAC1
Dma.RequestsNb=3
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FATFS.IPParameters=_CODE_PAGE,_USE_LFN
FATFS._CODE_PAGE=936
FATFS._USE_LFN=3
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP10=TIM3
Mcu.IP11=TIM6
Mcu.IP12=TIM14
Mcu.IP13=USART1
Mcu.IP2=DMA
Mcu.IP3=FATFS
Mcu.IP4=I2C1
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SDIO
Mcu.IP8=SPI2
Mcu.IP9=SYS
Mcu.IPNb=14
Mcu.Name=STM32F429Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC14/OSC32_IN
Mcu.Pin1=PC15/OSC32_OUT
Mcu.Pin10=PE10
Mcu.Pin11=PE11
Mcu.Pin12=PE12
Mcu.Pin13=PB12
Mcu.Pin14=PB13
Mcu.Pin15=PB14
Mcu.Pin16=PB15
Mcu.Pin17=PD10
Mcu.Pin18=PD11
Mcu.Pin19=PD12
Mcu.Pin2=PH0/OSC_IN
Mcu.Pin20=PD13
Mcu.Pin21=PD14
Mcu.Pin22=PD15
Mcu.Pin23=PC8
Mcu.Pin24=PC9
Mcu.Pin25=PA9
Mcu.Pin26=PA10
Mcu.Pin27=PA13
Mcu.Pin28=PA14
Mcu.Pin29=PC10
Mcu.Pin3=PH1/OSC_OUT
Mcu.Pin30=PC11
Mcu.Pin31=PC12
Mcu.Pin32=PD2
Mcu.Pin33=PB8
Mcu.Pin34=PB9
Mcu.Pin35=VP_FATFS_VS_SDIO
Mcu.Pin36=VP_SYS_VS_Systick
Mcu.Pin37=VP_TIM3_VS_ClockSourceINT
Mcu.Pin38=VP_TIM6_VS_ClockSourceINT
Mcu.Pin39=VP_TIM14_VS_ClockSourceINT
Mcu.Pin4=PC0
Mcu.Pin5=PA4
Mcu.Pin6=PA5
Mcu.Pin7=PE7
Mcu.Pin8=PE8
Mcu.Pin9=PE9
Mcu.PinsNb=40
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA4.Locked=true
PA4.Signal=COMP_DAC1_group
PA5.Signal=ADCx_IN5
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB12.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PB12.GPIO_Label=CS
PB12.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB12.Locked=true
PB12.PinState=GPIO_PIN_SET
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.Locked=true
PC0.Signal=ADCx_IN10
PC10.Mode=SD_4_bits_Wide_bus
PC10.Signal=SDIO_D2
PC11.Mode=SD_4_bits_Wide_bus
PC11.Signal=SDIO_D3
PC12.Mode=SD_4_bits_Wide_bus
PC12.Signal=SDIO_CK
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PC8.Mode=SD_4_bits_Wide_bus
PC8.Signal=SDIO_D0
PC9.Mode=SD_4_bits_Wide_bus
PC9.Signal=SDIO_D1
PD10.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PD10.GPIO_Label=LED1
PD10.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD10.GPIO_PuPd=GPIO_NOPULL
PD10.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD10.Locked=true
PD10.PinState=GPIO_PIN_SET
PD10.Signal=GPIO_Output
PD11.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PD11.GPIO_Label=LED2
PD11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD11.GPIO_PuPd=GPIO_NOPULL
PD11.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD11.Locked=true
PD11.PinState=GPIO_PIN_SET
PD11.Signal=GPIO_Output
PD12.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PD12.GPIO_Label=LED3
PD12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD12.GPIO_PuPd=GPIO_NOPULL
PD12.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD12.Locked=true
PD12.PinState=GPIO_PIN_SET
PD12.Signal=GPIO_Output
PD13.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PD13.GPIO_Label=LED4
PD13.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD13.GPIO_PuPd=GPIO_NOPULL
PD13.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD13.Locked=true
PD13.PinState=GPIO_PIN_SET
PD13.Signal=GPIO_Output
PD14.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PD14.GPIO_Label=LED5
PD14.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD14.GPIO_PuPd=GPIO_NOPULL
PD14.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD14.Locked=true
PD14.PinState=GPIO_PIN_SET
PD14.Signal=GPIO_Output
PD15.GPIOParameters=GPIO_Speed,PinState,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PD15.GPIO_Label=LED6
PD15.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD15.GPIO_PuPd=GPIO_NOPULL
PD15.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PD15.Locked=true
PD15.PinState=GPIO_PIN_SET
PD15.Signal=GPIO_Output
PD2.Mode=SD_4_bits_Wide_bus
PD2.Signal=SDIO_CMD
PE10.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE10.GPIO_Label=button4
PE10.GPIO_Mode=GPIO_MODE_INPUT
PE10.GPIO_PuPd=GPIO_PULLUP
PE10.Locked=true
PE10.Signal=GPIO_Input
PE11.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE11.GPIO_Label=button5
PE11.GPIO_Mode=GPIO_MODE_INPUT
PE11.GPIO_PuPd=GPIO_PULLUP
PE11.Locked=true
PE11.Signal=GPIO_Input
PE12.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE12.GPIO_Label=button6
PE12.GPIO_Mode=GPIO_MODE_INPUT
PE12.GPIO_PuPd=GPIO_PULLUP
PE12.Locked=true
PE12.Signal=GPIO_Input
PE7.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE7.GPIO_Label=button1
PE7.GPIO_Mode=GPIO_MODE_INPUT
PE7.GPIO_PuPd=GPIO_PULLUP
PE7.Locked=true
PE7.Signal=GPIO_Input
PE8.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE8.GPIO_Label=button2
PE8.GPIO_Mode=GPIO_MODE_INPUT
PE8.GPIO_PuPd=GPIO_PULLUP
PE8.Locked=true
PE8.Signal=GPIO_Input
PE9.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_Mode
PE9.GPIO_Label=button3
PE9.GPIO_Mode=GPIO_MODE_INPUT
PE9.GPIO_PuPd=GPIO_PULLUP
PE9.Locked=true
PE9.Signal=GPIO_Input
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x1000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=GD32_DEMO_01_LED.ioc
ProjectManager.ProjectName=GD32_DEMO_01_LED
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x2000
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_DAC_Init-DAC-false-HAL-true,8-MX_TIM6_Init-TIM6-false-HAL-true,9-MX_I2C1_Init-I2C1-false-HAL-true,10-MX_TIM14_Init-TIM14-false-HAL-true,11-MX_SPI2_Init-SPI2-false-HAL-true,12-MX_SDIO_SD_Init-SDIO-false-HAL-true,13-MX_FATFS_Init-FATFS-false-HAL-false
RCC.48MHZClocksFreq_Value=45000000
RCC.AHBFreq_Value=180000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=45000000
RCC.APB1TimFreq_Value=90000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=90000000
RCC.APB2TimFreq_Value=180000000
RCC.CortexFreq_Value=180000000
RCC.EthernetFreq_Value=180000000
RCC.FCLKCortexFreq_Value=180000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=180000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQ,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=180000000
RCC.PLLCLKFreq_Value=180000000
RCC.PLLM=15
RCC.PLLN=216
RCC.PLLQ=8
RCC.PLLQCLKFreq_Value=45000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=180000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=360000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VCOSAIOutputFreq_ValueR=40833333.333333336
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
SDIO.ClockDiv=6
SDIO.IPParameters=ClockDiv
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SH.ADCx_IN5.0=ADC1_IN5,IN5
SH.ADCx_IN5.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SPI2.CLKPhase=SPI_PHASE_2EDGE
SPI2.CLKPolarity=SPI_POLARITY_HIGH
SPI2.CalculateBaudRate=22.5 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,CLKPolarity,CLKPhase
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
TIM14.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM14.IPParameters=Prescaler,Period,AutoReloadPreload
TIM14.Period=5000
TIM14.Prescaler=180-1
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM3.Period=999
TIM3.Prescaler=89
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM6.Period=999
TIM6.Prescaler=89
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_FATFS_VS_SDIO.Mode=SDIO
VP_FATFS_VS_SDIO.Signal=FATFS_VS_SDIO
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM14_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM14_VS_ClockSourceINT.Signal=TIM14_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
