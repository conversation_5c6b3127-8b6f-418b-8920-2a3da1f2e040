--cpu=Cortex-M4.fp.sp
"gd32_demo_01_led\startup_stm32f429xx.o"
"gd32_demo_01_led\main.o"
"gd32_demo_01_led\gpio.o"
"gd32_demo_01_led\adc.o"
"gd32_demo_01_led\dac.o"
"gd32_demo_01_led\dma.o"
"gd32_demo_01_led\i2c.o"
"gd32_demo_01_led\sdio.o"
"gd32_demo_01_led\spi.o"
"gd32_demo_01_led\tim.o"
"gd32_demo_01_led\usart.o"
"gd32_demo_01_led\stm32f4xx_it.o"
"gd32_demo_01_led\stm32f4xx_hal_msp.o"
"gd32_demo_01_led\stm32f4xx_hal_adc.o"
"gd32_demo_01_led\stm32f4xx_hal_adc_ex.o"
"gd32_demo_01_led\stm32f4xx_ll_adc.o"
"gd32_demo_01_led\stm32f4xx_hal_rcc.o"
"gd32_demo_01_led\stm32f4xx_hal_rcc_ex.o"
"gd32_demo_01_led\stm32f4xx_hal_flash.o"
"gd32_demo_01_led\stm32f4xx_hal_flash_ex.o"
"gd32_demo_01_led\stm32f4xx_hal_flash_ramfunc.o"
"gd32_demo_01_led\stm32f4xx_hal_gpio.o"
"gd32_demo_01_led\stm32f4xx_hal_dma_ex.o"
"gd32_demo_01_led\stm32f4xx_hal_dma.o"
"gd32_demo_01_led\stm32f4xx_hal_pwr.o"
"gd32_demo_01_led\stm32f4xx_hal_pwr_ex.o"
"gd32_demo_01_led\stm32f4xx_hal_cortex.o"
"gd32_demo_01_led\stm32f4xx_hal.o"
"gd32_demo_01_led\stm32f4xx_hal_exti.o"
"gd32_demo_01_led\stm32f4xx_hal_dac.o"
"gd32_demo_01_led\stm32f4xx_hal_dac_ex.o"
"gd32_demo_01_led\stm32f4xx_hal_i2c.o"
"gd32_demo_01_led\stm32f4xx_hal_i2c_ex.o"
"gd32_demo_01_led\stm32f4xx_ll_sdmmc.o"
"gd32_demo_01_led\stm32f4xx_hal_sd.o"
"gd32_demo_01_led\stm32f4xx_hal_mmc.o"
"gd32_demo_01_led\stm32f4xx_hal_spi.o"
"gd32_demo_01_led\stm32f4xx_hal_tim.o"
"gd32_demo_01_led\stm32f4xx_hal_tim_ex.o"
"gd32_demo_01_led\stm32f4xx_hal_uart.o"
"gd32_demo_01_led\system_stm32f4xx.o"
"gd32_demo_01_led\ebtn.o"
"gd32_demo_01_led\ringbuffer.o"
"gd32_demo_01_led\oled.o"
"gd32_demo_01_led\mui.o"
"gd32_demo_01_led\mui_u8g2.o"
"gd32_demo_01_led\u8g2_arc.o"
"gd32_demo_01_led\u8g2_bitmap.o"
"gd32_demo_01_led\u8g2_box.o"
"gd32_demo_01_led\u8g2_buffer.o"
"gd32_demo_01_led\u8g2_button.o"
"gd32_demo_01_led\u8g2_circle.o"
"gd32_demo_01_led\u8g2_cleardisplay.o"
"gd32_demo_01_led\u8g2_d_memory.o"
"gd32_demo_01_led\u8g2_d_setup.o"
"gd32_demo_01_led\u8g2_font.o"
"gd32_demo_01_led\u8g2_fonts.o"
"gd32_demo_01_led\u8g2_hvline.o"
"gd32_demo_01_led\u8g2_input_value.o"
"gd32_demo_01_led\u8g2_intersection.o"
"gd32_demo_01_led\u8g2_kerning.o"
"gd32_demo_01_led\u8g2_line.o"
"gd32_demo_01_led\u8g2_ll_hvline.o"
"gd32_demo_01_led\u8g2_message.o"
"gd32_demo_01_led\u8g2_polygon.o"
"gd32_demo_01_led\u8g2_selection_list.o"
"gd32_demo_01_led\u8g2_setup.o"
"gd32_demo_01_led\u8log.o"
"gd32_demo_01_led\u8log_u8g2.o"
"gd32_demo_01_led\u8log_u8x8.o"
"gd32_demo_01_led\u8x8_8x8.o"
"gd32_demo_01_led\u8x8_byte.o"
"gd32_demo_01_led\u8x8_cad.o"
"gd32_demo_01_led\u8x8_capture.o"
"gd32_demo_01_led\u8x8_d_ssd1306_128x32.o"
"gd32_demo_01_led\u8x8_debounce.o"
"gd32_demo_01_led\u8x8_display.o"
"gd32_demo_01_led\u8x8_fonts.o"
"gd32_demo_01_led\u8x8_gpio.o"
"gd32_demo_01_led\u8x8_input_value.o"
"gd32_demo_01_led\u8x8_message.o"
"gd32_demo_01_led\u8x8_selection_list.o"
"gd32_demo_01_led\u8x8_setup.o"
"gd32_demo_01_led\u8x8_string.o"
"gd32_demo_01_led\u8x8_u8toa.o"
"gd32_demo_01_led\u8x8_u16toa.o"
"gd32_demo_01_led\wououi.o"
"gd32_demo_01_led\wououi_anim.o"
"gd32_demo_01_led\wououi_font.o"
"gd32_demo_01_led\wououi_graph.o"
"gd32_demo_01_led\wououi_msg.o"
"gd32_demo_01_led\wououi_page.o"
"gd32_demo_01_led\wououi_user.o"
"gd32_demo_01_led\wououi_win.o"
"gd32_demo_01_led\gd25qxx.o"
"gd32_demo_01_led\lfs.o"
"gd32_demo_01_led\lfs_port.o"
"gd32_demo_01_led\lfs_util.o"
"gd32_demo_01_led\key_app.o"
"gd32_demo_01_led\led_app.o"
"gd32_demo_01_led\scheduler.o"
"gd32_demo_01_led\btn_app.o"
"gd32_demo_01_led\usart_app.o"
"gd32_demo_01_led\adc_app.o"
"gd32_demo_01_led\oled_app.o"
"gd32_demo_01_led\flash_app.o"
"gd32_demo_01_led\bsp_driver_sd.o"
"gd32_demo_01_led\sd_diskio.o"
"gd32_demo_01_led\fatfs.o"
"gd32_demo_01_led\diskio.o"
"gd32_demo_01_led\ff.o"
"gd32_demo_01_led\ff_gen_drv.o"
"gd32_demo_01_led\syscall.o"
"gd32_demo_01_led\cc936.o"
--library_type=microlib --strict --scatter "GD32_DEMO_01_LED\GD32_DEMO_01_LED.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32_DEMO_01_LED.map" -o GD32_DEMO_01_LED\GD32_DEMO_01_LED.axf